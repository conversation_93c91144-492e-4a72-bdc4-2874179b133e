<#-- (c) https://github.com/MontiCore/monticore -->
${tc.signature("domainClass", "name", "attributeMetric")}

<#assign chartContent>
<#if attributeMetric.recommendedChart?? && attributeMetric.isVisualizable>
  <#assign chartType = attributeMetric.recommendedChart>

  <#-- Route to appropriate chart component based on recommended chart type -->
  <#switch chartType>
    <#case "PIE_CHART">
      ${tc.includeArgs("tpl.metrics.charts.pie-chart", [domainClass, name, attributeMetric])}
      <#break>

    <#case "BAR_CHART">
      ${tc.includeArgs("tpl.metrics.charts.bar-chart", [domainClass, name, attributeMetric])}
      <#break>

    <#case "LINE_CHART">
      ${tc.includeArgs("tpl.metrics.charts.line-chart", [domainClass, name, attributeMetric])}
      <#break>

    <#case "GAUGE_CHART">
      ${tc.includeArgs("tpl.metrics.charts.gauge-chart", [domainClass, name, attributeMetric])}
      <#break>

    <#case "BULLET_CHART">
      ${tc.includeArgs("tpl.metrics.charts.bullet-chart", [domainClass, name, attributeMetric])}
      <#break>

    <#case "CANDLESTICK_CHART">
      ${tc.includeArgs("tpl.metrics.charts.candlestick-chart", [domainClass, name, attributeMetric])}
      <#break>

    <#case "HEATMAP_CHART">
      ${tc.includeArgs("tpl.metrics.charts.heatmap-chart", [domainClass, name, attributeMetric])}
      <#break>

    <#case "SUNBURST_CHART">
      ${tc.includeArgs("tpl.metrics.charts.sunburst-chart", [domainClass, name, attributeMetric])}
      <#break>

    <#case "SCATTER_PLOT">
      ${tc.includeArgs("tpl.metrics.charts.scatter-plot", [domainClass, name, attributeMetric])}
      <#break>

    <#case "DATA_TABLE">
      ${tc.includeArgs("tpl.metrics.charts.enhanced-table-component", [domainClass, name, attributeMetric])}
      <#break>

    <#default>
      ${tc.includeArgs("tpl.metrics.charts.text-display-component", [domainClass, name, attributeMetric])}
  </#switch>
<#else>
  <#-- No recommended chart or not visualizable - use text display -->
  ${tc.includeArgs("tpl.metrics.charts.text-display-component", [domainClass, name, attributeMetric])}
</#if>
</#assign>





${name?uncap_first}_${attributeMetric.attributeName}ChartCard@GemCard(
  width = "45%",
  height = "220px",
  title = "${attributeMetric.attributeName} Analysis",
  component = @GemColumn(
    hAlign = "center",
    vAlign = "center",
    components = [
      ${chartContent}
      <#if attributeMetric.visualizationHint?? && attributeMetric.visualizationHint.description??>,
      @GemText(
        value = "${attributeMetric.visualizationHint.description}",
      )
      </#if>
    ]
  )
);
